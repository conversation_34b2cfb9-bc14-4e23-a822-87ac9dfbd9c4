# 接口字段差异处理示例

## 问题描述
家长和老师的通讯录接口返回的数据字段不同：
- 家长接口：使用 `adlist` 字段
- 老师接口：使用 `classAddBookDTOList` 字段

## 解决方案

### 1. 修改前的代码
```javascript
// 原始代码 - 只支持家长接口的 adlist 字段
formatAddressBookData(rawData) {
    const { adlist = [], schoolBadgeUrl = '', schoolName = '' } = rawData
    
    return {
        classList: adlist.map(classItem => ({
            id: classItem.id,
            name: classItem.name,
            studentList: this.formatStudentList(classItem.studentList || [])
        })),
        schoolInfo: {
            badgeUrl: schoolBadgeUrl,
            name: schoolName
        }
    }
}
```

### 2. 修改后的代码
```javascript
// 新代码 - 支持两种不同的字段格式
formatAddressBookData(rawData, roleCode) {
    // 根据角色获取不同的字段
    let classList = []
    let schoolBadgeUrl = ''
    let schoolName = ''
    
    if (roleCode === 'eltern') {
        // 家长接口字段: adlist
        const { adlist = [], schoolBadgeUrl: badgeUrl = '', schoolName: name = '' } = rawData
        classList = adlist
        schoolBadgeUrl = badgeUrl
        schoolName = name
    } else {
        // 老师接口字段: classAddBookDTOList
        const { classAddBookDTOList = [], schoolBadgeUrl: badgeUrl = '', schoolName: name = '' } = rawData
        classList = classAddBookDTOList
        schoolBadgeUrl = badgeUrl
        schoolName = name
    }
    
    return {
        classList: classList.map(classItem => ({
            id: classItem.id,
            name: classItem.name,
            studentList: this.formatStudentList(classItem.studentList || [])
        })),
        schoolInfo: {
            badgeUrl: schoolBadgeUrl,
            name: schoolName
        }
    }
}
```

### 3. 调用方式修改
```javascript
// 修改前
return this.formatAddressBookData(response.data)

// 修改后 - 传入角色参数
return this.formatAddressBookData(response.data, roleCode)
```

## 数据示例

### 家长接口返回数据
```json
{
    "adlist": [
        {
            "id": "784148883587465218",
            "name": "3班",
            "studentList": [
                {
                    "id": "1869559158148198401",
                    "name": "小小静",
                    "className": "3班",
                    "avatar": "",
                    "gender": 0,
                    "address": "地址信息",
                    "phone": ""
                }
            ]
        }
    ],
    "schoolBadgeUrl": "https://example.com/badge.png",
    "schoolName": "示例小学"
}
```

### 老师接口返回数据
```json
{
    "classAddBookDTOList": [
        {
            "id": "784148883587465218",
            "name": "3班", 
            "studentList": [
                {
                    "id": "1869559158148198401",
                    "name": "小小静",
                    "className": "3班",
                    "avatar": "",
                    "gender": 0,
                    "address": "地址信息",
                    "phone": ""
                }
            ]
        }
    ],
    "schoolBadgeUrl": "https://example.com/badge.png",
    "schoolName": "示例小学"
}
```

## 处理流程

```mermaid
graph TD
    A[接收接口数据] --> B{判断用户角色}
    B -->|家长 eltern| C[提取 adlist 字段]
    B -->|老师 其他| D[提取 classAddBookDTOList 字段]
    C --> E[统一格式化处理]
    D --> E
    E --> F[返回标准化数据]
```

## 测试验证

### 1. 家长角色测试
```javascript
// 模拟家长接口数据
const parentData = {
    adlist: [
        { id: "1", name: "1班", studentList: [...] },
        { id: "2", name: "2班", studentList: [...] }
    ],
    schoolBadgeUrl: "badge.png",
    schoolName: "测试学校"
}

const result = addressBookService.formatAddressBookData(parentData, 'eltern')
console.log('家长数据处理结果:', result)
```

### 2. 老师角色测试
```javascript
// 模拟老师接口数据
const teacherData = {
    classAddBookDTOList: [
        { id: "1", name: "1班", studentList: [...] },
        { id: "2", name: "2班", studentList: [...] }
    ],
    schoolBadgeUrl: "badge.png", 
    schoolName: "测试学校"
}

const result = addressBookService.formatAddressBookData(teacherData, 'teacher')
console.log('老师数据处理结果:', result)
```

## 关键优势

1. **向后兼容**: 保持原有功能不变
2. **角色适配**: 自动根据角色处理不同字段
3. **统一输出**: 无论输入格式如何，输出格式保持一致
4. **易于维护**: 集中处理字段差异，便于后续扩展

## 注意事项

1. **角色识别**: 确保传入正确的 `roleCode` 参数
2. **字段映射**: 新增角色时需要在条件判断中添加对应的字段映射
3. **数据验证**: 建议添加数据格式验证，确保接口返回的数据结构正确
4. **错误处理**: 当字段不存在时，使用默认值避免程序崩溃

## 扩展建议

如果将来有更多角色或字段差异，可以考虑使用配置化的方式：

```javascript
const FIELD_MAPPING = {
    'eltern': {
        classList: 'adlist',
        schoolBadge: 'schoolBadgeUrl',
        schoolName: 'schoolName'
    },
    'teacher': {
        classList: 'classAddBookDTOList',
        schoolBadge: 'schoolBadgeUrl', 
        schoolName: 'schoolName'
    }
    // 可以继续添加新角色...
}
```

这样可以更灵活地处理各种字段差异情况。
