<template>
    <view class="custom-tabbar">
        <!-- 页面内容区域 -->
        <view class="page-content" :style="{ paddingBottom: tabbarHeight + 'px' }">
            <slot />
        </view>

        <!-- 自定义tabbar -->
        <view class="tabbar-container">
            <view class="tabbar-item" v-for="(item, index) in tabList" :key="item.pagePath" :class="{ active: isCurrentPage(item.pagePath) }" @click="switchTab(item)">
                <image class="tabbar-icon" :src="getIconPath(item)" />
                <text class="tabbar-text" :style="{ color: getTextColor(item) }">
                    {{ item.text }}
                </text>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, computed } from "vue"

import {onPageShow } from "@dcloudio/uni-app"


const props = defineProps({
    tabList: {
        type: Array,
        required: true
    }
})

const tabbarHeight = 50
const currentPath = ref("")

// 获取当前页面路径
const getCurrentPath = () => {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    return "/" + currentPage.route
}

// 判断是否为当前页面
const isCurrentPage = (pagePath) => {
    return currentPath.value === pagePath
}

// 获取图标路径
const getIconPath = (item) => {
    return isCurrentPage(item.pagePath) ? item.selectedIconPath : item.iconPath
}

// 获取文字颜色
const getTextColor = (item) => {
    return isCurrentPage(item.pagePath) ? "#00b781" : "#999999"
}

// 切换tab
const switchTab = (item) => {
    if (isCurrentPage(item.pagePath)) return

    uni.navigateTo({
        url: item.pagePath
    })
}

onPageShow(() => {
    currentPath.value = getCurrentPath()
})
</script>

<style lang="scss" scoped>
.custom-tabbar {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.page-content {
    flex: 1;
    overflow: hidden;
}

.tabbar-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: #fff;
    border-top: 1px solid #e5e5e5;
    display: flex;
    z-index: 9999;
}

.tabbar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &.active {
        .tabbar-text {
            color: var(--primary-color);;
        }
    }
}

.tabbar-icon {
    width: 22px;
    height: 22px;
    margin-bottom: 2px;
}

.tabbar-text {
    font-size: 10px;
    color: #999999;
}
</style>
