import { defineConfig, loadEnv } from "vite"
import uni from "@dcloudio/vite-plugin-uni"
import AutoImport from "unplugin-auto-import/vite"
import Components from "unplugin-vue-components/vite"
import path from "path"
import { createHtmlPlugin } from "vite-plugin-html"
import { visualizer } from "rollup-plugin-visualizer"

// https://vitejs.dev/config/

function isSubstrExist(str, substr) {
    var regex = new RegExp(substr)
    return regex.test(str)
}

const replaceFilePlugin = (replacements) => {
    return {
        name: "replace-file-plugin",
        enforce: "pre",
        // 在其他钩子中可以访问到配置
        transform(code, id) {
            if (["node_modules", "uni_modules"].find((i) => isSubstrExist(id, i))) return code
            if ([".vue", ".css", ".scss", "js"].find((i) => id.endsWith(i))) {
                Object.entries(replacements).forEach(([find, replaceWith]) => {
                    code = code.replace(new RegExp(find, "g"), replaceWith)
                })
                return code
            }
            return code
        }
    }
}

const versionTime = new Date().getTime()

function updateVersionPlugin(version = +new Date()) {
    return {
        name: "update-version-plugin",
        closeBundle() {
            if (process.env.UNI_PLATFORM == "h5") {
                console.log("H5平台生产version.json")
                const fs = require("fs")
                // eslint-disable-next-line quotes
                fs.writeFile("dist/build/h5/version.json", '{"version":' + version + "}\n", (err) => {
                    if (err) {
                        return console.log(err)
                    }
                })
            }
        }
    }
}

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, process.cwd())
    return {
        resolve: {
            extensions: [".mjs", ".js", ".mts", ".ts", ".jsx", ".tsx", ".json", ".vue", ".scss"],
            alias: {
                "@": path.resolve(__dirname, "src"),
                "@tinymce/tinymce-vue": "node_modules/@tinymce/tinymce-vue",
                "vue-signature-pad": "vue-signature-pad"
            }
        },
        plugins: [
            uni(),
            // visualizer({
            //     open: true, //注意这里要设置为true，否则无效
            //     filename: "stats.html", //分析图生成的文件名
            //     gzipSize: true, // 收集 gzip 大小并将其显示
            //     brotliSize: true // 收集 brotli 大小并将其显示
            // }),
            AutoImport({
                imports: ["vue", "uni-app", "pinia"],
                dts: false,
                eslintrc: { enabled: false },
                vueTemplate: true, // default false
                dirs: ["src/store", "src/utils", "src/hooks"]
            }),
            Components({
                dirs: ["src/components"],
                extensions: ["vue"],
                deep: true // 搜索子目录//组件名称包含目录，防止同名组件冲突
            }),
            replaceFilePlugin({
                "@nginx": env.VITE_BASE_STATIC
            }),
            createHtmlPlugin({
                minify: true,
                inject: {
                    data: {
                        version: versionTime
                    }
                }
            })
        ],
        build: {
            // sourcemap: true,
            rollupOptions: {
                plugins: [updateVersionPlugin(versionTime)]
            }
        }
    }
})
