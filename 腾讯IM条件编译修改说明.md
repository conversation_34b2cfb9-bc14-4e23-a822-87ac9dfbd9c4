# 腾讯IM TUIKit 条件编译修改说明

## 修改概述

本次修改将腾讯IM TUIKit相关代码配置为条件编译，确保：
- **H5平台**：正常包含所有TUIKit功能
- **微信小程序**：完全排除TUIKit代码，避免构建错误和包体积增大

## 修改文件清单

### 1. `src/pages.json`
- ✅ 为TUIKit分包添加 `#ifdef H5` 条件编译
- ✅ 为TIM相关页面路由添加 `#ifdef H5` 条件编译  
- ✅ 为preloadRule中的TUIKit包预加载添加条件编译

### 2. `src/App.vue`
- ✅ 为TUILogin和TUIChatKit导入添加 `#ifdef H5` 条件编译
- ✅ 为TUILogin登录逻辑添加 `#ifdef H5` 条件编译

### 3. `src/utils/timUnreadHelper.js`
- ✅ 为腾讯IM SDK导入添加 `#ifdef H5` 条件编译
- ✅ 为所有TIM相关函数添加平台条件判断
- ✅ 微信小程序端返回默认值（0或空函数）

### 4. `src/pages/chat/index.vue`
- ✅ 为TUILogin导入添加 `#ifdef H5` 条件编译
- ✅ 为TIM登录和未读消息获取逻辑添加条件编译

### 5. `src/package/chat/tim/index.vue`
- ✅ 为TUIConversation组件导入添加 `#ifdef H5` 条件编译
- ✅ 为模板中的TUIConversation使用添加条件编译
- ✅ 微信小程序端显示功能不可用提示

### 6. `src/package/chat/tim/contacts.vue`
- ✅ 为TUIContact组件导入添加 `#ifdef H5` 条件编译
- ✅ 为模板中的TUIContact使用添加条件编译
- ✅ 微信小程序端显示功能不可用提示

## 条件编译说明

### H5平台 (`#ifdef H5`)
```javascript
// #ifdef H5
import { TUILogin } from "@tencentcloud/tui-core"
import { TUIChatKit } from "./TUIKit/index.ts"
// 正常的TIM功能代码
// #endif
```

### 微信小程序平台 (`#ifdef MP-WEIXIN`)
```javascript
// #ifdef MP-WEIXIN
// 返回默认值或显示占位符
return Promise.resolve(0)
// #endif
```

## 功能影响

### H5平台
- ✅ 完整的腾讯IM聊天功能
- ✅ 会话列表和联系人管理
- ✅ 未读消息数统计
- ✅ 所有TUIKit组件正常工作

### 微信小程序平台
- ✅ 不包含任何TUIKit代码，减小包体积
- ✅ 避免小程序不支持的API导致的构建错误
- ✅ TIM相关页面显示友好的功能不可用提示
- ✅ 未读消息数始终返回0，不影响其他功能

## 验证方法

运行测试脚本验证配置：
```bash
node test-build.js
```

或手动检查：
1. H5构建时应包含TUIKit相关代码
2. 微信小程序构建时应排除TUIKit相关代码
3. 小程序端访问TIM页面应显示占位符

## 注意事项

1. **TUIKit目录保留**：`src/TUIKit/` 目录仍然存在，但只在H5构建时被引用
2. **路由配置**：TIM相关页面路由只在H5平台注册
3. **依赖包**：`@tencentcloud/*` 相关包仍在package.json中，但只在H5构建时被打包
4. **向后兼容**：不影响现有的其他功能和页面

## 构建命令

- **H5构建**：`npm run build:h5` - 包含完整TIM功能
- **小程序构建**：`npm run build:mp-weixin` - 排除TIM功能

修改完成后，项目可以正常在两个平台构建，微信小程序端不会包含任何腾讯IM相关代码。
