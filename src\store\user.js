/*
 * @Description:
 * @Date: 2024-11-11 10:22:12
 * @LastEditors: lhq
 * @LastEditTime: 2024-11-11 10:37:27
 * @FilePath: \code\cloud-mobile\src\store\user.js
 */
/*
 * @Description:
 * @Date: 2024-10-25 11:37:45
 * @LastEditors: lhq
 * @LastEditTime: 2024-11-06 11:35:34
 * @FilePath: \code\校壹通\call-connect-wxprogram\src\store\user.js
 */
import { defineStore } from "pinia"
import { TUILogin } from "@tencentcloud/tui-core"
import { isTIMReady } from "@/utils/timUnreadHelper.js"
const useUserStore = defineStore("user", {
    state: () => {
        return {
            user: {}
        }
    },
    getters: {
        userInfo(state) {
            return state.user.userInfo
        },
        sigGen(state) {
            return state.user.userInfo.sigGen
        },
        identityInfo(state) {
            return state.user.identityInfo
        },
        schoolInfo(state) {
            return state.user.schoolInfo
        },
        studentInfo(state) {
            return state.user.studentInfo
        }
    },
    actions: {
        // 用户信息
        setUserInfo(userInfo) {
            this.user.userInfo = userInfo
        },
        async setUserSigGen(sigGen) {
            this.user.userInfo.sigGen = sigGen

            // 检查TIM是否已经就绪，避免重复登录
            if (isTIMReady()) {
                console.log("TIM已经就绪，跳过重复登录")
                return
            }

            await TUILogin.login({
                SDKAppID: sigGen.sdkAppId,
                userID: sigGen.identifier,
                userSig: sigGen.userSig,
                useUploadPlugin: true,
                framework: "vue3"
            })
        },
        // 角色信息
        setIdentityInfo(identityInfo) {
            this.user.identityInfo = identityInfo
            // 根据角色设置一个默认头像进去
            if (identityInfo.roleCode == "eltern") {
                this.user.userInfo.defaultAvatar = "@nginx/components/parent.png"
            } else if (identityInfo.roleCode == "student") {
                this.user.userInfo.defaultAvatar = "@nginx/components/student.png"
            } else {
                this.user.userInfo.defaultAvatar = "@nginx/components/teacher.png"
            }
        },
        // 任课老师 任教的班级
        setClassTeachList(classesList) {
            this.user.userInfo.classesTeachList = classesList
        },
        // 班主任 任教的班级
        setClassMaterList(classesList) {
            this.user.userInfo.classesMaterList = classesList
        },
        // 学校信息
        setSchoolInfo(schoolInfo) {
            this.user.schoolInfo = schoolInfo
        },
        // 家长下的孩子信息
        setStudentInfo(studentInfo) {
            this.user.studentInfo = studentInfo
        }
    },

    persist: {
        key: "yd-mobile-user",
        paths: ["user"],
        debug: import.meta.env.VITE_USER_NODE_ENV === "production",
        beforeRestore: (ctx) => {
            console.log(`beforeRestore '${ctx.store.$id}'`)
        },
        afterRestore: (ctx) => {
            console.log(`afterRestore '${ctx.store.$id}'`)
        }
    }
})

export default useUserStore
