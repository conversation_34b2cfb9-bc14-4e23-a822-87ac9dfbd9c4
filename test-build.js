#!/usr/bin/env node

/**
 * 测试构建脚本 - 验证腾讯IM条件编译是否正确
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始验证腾讯IM条件编译配置...\n');

// 检查的文件列表
const filesToCheck = [
    'src/pages.json',
    'src/App.vue', 
    'src/pages/chat/index.vue',
    'src/utils/timUnreadHelper.js',
    'src/package/chat/tim/index.vue',
    'src/package/chat/tim/contacts.vue'
];

let hasErrors = false;

// 检查每个文件
filesToCheck.forEach(filePath => {
    console.log(`📁 检查文件: ${filePath}`);
    
    if (!fs.existsSync(filePath)) {
        console.log(`   ❌ 文件不存在`);
        hasErrors = true;
        return;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否包含条件编译指令
    const hasH5Ifdef = content.includes('#ifdef H5');
    const hasMPWeixinIfdef = content.includes('#ifdef MP-WEIXIN') || content.includes('#ifndef H5');
    const hasEndif = content.includes('#endif');
    
    // 检查是否包含腾讯IM相关导入
    const hasTUIImports = content.includes('@tencentcloud/') || content.includes('TUIKit/');
    
    console.log(`   📋 条件编译检查:`);
    console.log(`      - H5条件编译: ${hasH5Ifdef ? '✅' : '❌'}`);
    console.log(`      - 小程序条件编译: ${hasMPWeixinIfdef ? '✅' : '⚠️ (可选)'}`);
    console.log(`      - 结束标记: ${hasEndif ? '✅' : (hasH5Ifdef ? '❌' : '⚠️')}`);
    
    if (hasTUIImports) {
        console.log(`   📦 腾讯IM导入: ${hasH5Ifdef ? '✅ 已条件编译' : '❌ 需要条件编译'}`);
        if (!hasH5Ifdef) {
            hasErrors = true;
        }
    } else {
        console.log(`   📦 腾讯IM导入: ⚠️ 未检测到`);
    }
    
    console.log('');
});

// 检查TUIKit目录是否存在
console.log('📁 检查TUIKit目录...');
if (fs.existsSync('src/TUIKit')) {
    console.log('   ✅ TUIKit目录存在 (H5构建时会包含)');
} else {
    console.log('   ❌ TUIKit目录不存在');
    hasErrors = true;
}

console.log('\n🎯 验证结果:');
if (hasErrors) {
    console.log('❌ 发现问题，请检查上述错误');
    process.exit(1);
} else {
    console.log('✅ 所有检查通过！');
    console.log('📝 说明:');
    console.log('   - H5构建时会包含TUIKit相关代码');
    console.log('   - 微信小程序构建时会排除TUIKit相关代码');
    console.log('   - TIM相关页面在小程序端会显示占位符');
}
