<template>
    <div id="app">
        <router-view />
    </div>
</template>

<script setup>
import { provide, ref, reactive } from "vue"
import { browserEnvironment, checkPlatform, sendAppEvent } from "@/utils/sendAppEvent.js"
import initRouter from "./router"
import initUpdate from "./utils/update"
import { setToken, getToken, removeToken } from "@/utils/storageToken.js"
import useStore from "@/store"
// #ifdef H5 || H5-WEIXIN
import * as dd from "dingtalk-jsapi" // 钉钉
import RSA from "@/utils/rsa.js"
// #endif
import http from "@/utils/http.js"
// 调试用
// import { initDingH5RemoteDebug } from "dingtalk-h5-remote-debug";
// initDingH5RemoteDebug();
// #ifdef H5
import { TUILogin } from "@tencentcloud/tui-core"
import { isTIMReady } from "@/utils/timUnreadHelper.js"
import { TUIChatKit } from "./TUIKit/index.ts"
TUIChatKit.init()
// #endif

// 响应式数据定义
const loginPath = ref("")

// 角色代码类型转换函数
const roleCodeType = (roleCode) => {
    console.log(roleCode, "roleCoderoleCoderoleCode")
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else if (roleCode == "dorm_admin") {
        return "dorm_admin"
    } else {
        return "ordinaryTeacher"
    }
}

// 身份类型转换函数
const identityType = (identity) => {
    if (identity == "eltern") {
        return 2
    } else if (identity == "student") {
        return 0
    } else {
        return 1
    }
}

// 获取学生信息
const getYourChildrenInfo = () => {
    const { user } = useStore()
    http.get("/app/student/getStudentList").then((res) => {
        const arr = res.data?.map((i) => {
            return {
                ...i,
                studentName: i.name,
                studentId: i.id
            }
        })
        user.setStudentInfo(arr)
    })
}

// 任课老师角色下管理的班级
const queryClassTeachList = () => {
    const { user } = useStore()
    http.get("/cloud/v3/classes/queryClassTeachList").then((res) => {
        user.setClassTeachList(res.data)
    })
}

// 班主任角色下管理的班级
const queryClassMaterList = () => {
    const { user } = useStore()
    http.get("/app/master/class/queryClassMaterList").then((res) => {
        user.setClassMaterList(res.data)
    })
}

// 获取当前学校类型 K12/大学
const getSchoolType = async () => {
    const { user } = useStore()
    await http
        .post("/app/school/template/getCommentKey", { commentKey: ["schoolType"] })
        .then((res) => {
            // 设置学校类型,schoolType: 1是K12，2是大学
            const info = { ...user.schoolInfo, schoolType: res.data.schoolType == 2 ? "university" : "K12" }
            user.setSchoolInfo(info)
        })
        .catch(() => {
            user.setSchoolInfo({ ...user.schoolInfo, schoolType: "K12" })
        })
}

// 设置本地用户信息
const setLocal = async (res) => {
    const { user, home, system, local } = useStore()
    const item = res.data?.identitysV3[0]
    const userInfo = {
        ...res.data,
        roleCode: roleCodeType(res.data?.roleCode),
        identity: identityType(res.data?.identity)
    }
    const identityInfo = {
        ...item,
        roleCode: roleCodeType(item?.roleCode),
        identity: identityType(item?.identity)
    }
    user.setUserInfo(userInfo)
    user.setIdentityInfo(identityInfo)
    // 如果选择了家长的角色 调用接口把孩子都查出来 然后存入store
    item.roleCode == "eltern" ? getYourChildrenInfo() : user.setStudentInfo([])
    // 获取任课老师和班主任任教的班级
    if (item.roleCode == "headmaster") {
        queryClassMaterList()
    } else if (item.roleCode == "teacher") {
        queryClassTeachList()
    }
    // 设置学校类型 K12/大学
    await getSchoolType()
    // 获取首页快捷入口
    await home.queryQuickList()
    // #ifdef H5 || MP-WEIXIN
    if (user.identityInfo.roleCode === "dorm_admin") {
        system.setPrimaryColor("#4566d5")
    } else {
        system.setPrimaryColor("#00B781")
    }
    // #endif
    system.switchTab({ id: system.tabBarList[0]?.id || 0 })
    // 根据角色tabBar
    if (loginPath.value == "pages/login/index") {
        if (local.share) {
            navigateTo({
                url: "/pages/sharePage/index",
                query: local.shareOption
            })
        } else {
            // 切换身份默认选中首页
            uni.reLaunch({ url: system.tabBarList[0].pagePath })
        }
    }
}

// 获取用户信息
const getUserInfo = (schoolId) => {
    http.get("/app/mobile/user/school")
        .then((userRes) => {
            const { user } = useStore()
            console.log("学校信息", userRes?.data)
            const school = userRes?.data?.find((item) => item.id == schoolId) || {}
            const role = school.childrenV3[0]
            const p = {
                schoolId,
                id: role && role.id,
                roleCode: role && role.roleCode
            }
            user.setSchoolInfo(school)
            console.log("login参数", p)
            http.post("/app/mobile/user/login", p)
                .then(async (res) => {
                    console.log("登录成功", res)
                    await setLocal(res)
                    uni.hideLoading({
                        success() {
                            uni.showToast({
                                title: "登录成功",
                                icon: "success"
                            })
                        }
                    })
                })
                .catch((err) => {
                    console.log("登录失败", err)
                })
        })
        .catch(() => {
            uni.showToast({
                title: "获取用户信息异常,授权失败",
                icon: "none",
                duration: 2000
            })
        })
}

// 获取URL参数
const getUrlParams = (_url) => {
    const params = {}
    const url = _url || window.location.search || window.location.hash
    if (url.indexOf("?") > -1) {
        const str = url.split("?")[1]
        const arr = str.split("&")
        arr.forEach((item) => {
            const [key, value] = item.split("=")
            params[key] = value
        })
        return params
    } else {
        return params
    }
}

// 微信参数配置
const wxParameter = reactive({
    // grant_type: "wx_mp_auto",
    // client_id: "yide-h5",
    // client_secret: "yide1234567",
    schoolId: ""
})

// 获取微信AppId
const getWxAppId = (schoolId) => {
    return http.get("/app/wx/appId/get", { schoolId }).then((res) => {
        return res.data
    })
}

// 微信授权
const wechatAuth = (appId) => {
    console.log("21212121212")

    return new Promise((resolve) => {
        const urlParams = getUrlParams()
        console.log(urlParams)

        if (urlParams.code) {
            resolve(urlParams.code)
        } else {
            console.log(121212121)

            const redirect_uri = encodeURIComponent(window.location.href)

            console.log(redirect_uri, "redirect_uri")

            const new_href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirect_uri}&response_type=code&scope=snsapi_base&state=123#wechat_redirect`
            window.location.href = new_href
            console.log("生效了没")

            console.log(window.location.href, "111111")
        }
    })
}

// 微信授权登录
const initWxAuth = (schoolId) => {
    getWxAppId(schoolId).then((appId) => {
        console.log("微信授权登录", appId)
        wxParameter.schoolId = schoolId
        wxParameter.appId = appId
        wechatAuth(appId).then((code) => {
            wxParameter.code = code
            console.log(code, "code")

            http.post("/app/wx/user/getOpenId", wxParameter)
                .then((res) => {
                    wxParameter.openId = res.data || ""
                    uni.hideLoading()
                    uni.setStorageSync("openId", res.data)

                    // setToken(res.data.cloudAccessToken)
                    // getUserInfo(schoolId)
                })
                .catch(() => {
                    uni.hideLoading()
                })
        })
    })
}

// 钉钉授权登录获取code
const initDingdingAuth = (corpId, schoolId) => {
    dd.ready(function () {
        dd.runtime.permission.requestAuthCode({
            corpId,
            onSuccess: async (result) => {
                const p = {
                    corpId,
                    schoolId,
                    code: result.code,
                    grant_type: "dingtalk", // 授权模式，默认password
                    client_id: "yide-dingtalk", // 客户端ID: yide-cloud云平台、yide-manage一德后台、yide-open开放平台
                    client_secret: "yide1234567" // 客户端密钥
                }
                console.log("登录参数：", p)
                try {
                    const passWad = {
                        paramEncipher: RSA.encrypt(JSON.stringify({ ...p, corpId, schoolId }))
                    }
                    const res = await http._fetch("/auth/oauth/token", passWad, "POST", { "Content-Type": "application/x-www-form-urlencoded" })
                    console.log("/auth/oauth/token  返回", res)
                    const { accessToken } = res.data
                    setToken(accessToken)
                    getUserInfo(schoolId)
                } catch (error) {
                    console.log(error)
                    uni.hideLoading()
                    uni.showToast({
                        title: "登录失败" + JSON.stringify(error),
                        icon: "none"
                    })
                }
            },
            onFail: (err) => {
                // gzh();
                uni.hideLoading()
                uni.showToast({
                    title: "登录失败：" + JSON.stringify(err),
                    icon: "none"
                })
            }
        })
    })
}

// 应用启动时的处理函数
const handleLaunch = async (item) => {
    loginPath.value = item.path
    if (item.path == "apps/intelligence/index") {
        // 设置token 用于在原生应用中进入自动token
        if (item.query.token) {
            setToken(item.query.token)
        }
    }
    // 路由初始化
    initRouter()
    // 更新初始化
    initUpdate()
    // 推送

    // 调试
    // initDebug()
    // #ifdef H5 || H5-WEIXIN
    if (checkPlatform() == "dingding") {
        console.log("______叮叮应用启动_____", item)
        const { corpId, schoolId } = item.query
        if (corpId && schoolId) {
            console.log(corpId, schoolId)

            // 钉钉环境，免密登录
            provide("isDingtalkAuth", 1)

            uni.showLoading({
                title: "登录中...",
                success() {
                    initDingdingAuth(corpId, schoolId)
                }
            })
        } else {
            console.log(`叮叮环境，免密登录失败，获取参数错误`)
            // uni.showToast({
            //     title: '登录失败，参数错误',
            //     icon: 'none',
            //     duration: 2000
            // })
        }
    }
    // 获取App返回的数据
    browserEnvironment()
    // #endif

    // #ifdef H5 || H5-WEIXIN
    if (checkPlatform() == "wx-miniprogram") {
        const { schoolId } = item.query
        if (schoolId) {
            // 有学校id 开始换走登录逻辑
            uni.showLoading({
                title: "登录中...",
                success() {
                    initWxAuth(schoolId)
                }
            })
        } else {
            console.log(`微信环境，免密登录失败，获取参数错误`)
        }
    }
    // #endif

    const { user, system } = useStore()
    const token = getToken()
    const whiteList = [] // 路由白名单
    if (!user.identityInfo && !token && whiteList.includes(item.path)) {
        setTimeout(() => {
            uni.clearStorageSync()
            uni.reLaunch({ url: "/pages/login/index" })
        }, 500)
    }
    // #ifdef H5 || MP-WEIXIN
    if (user.identityInfo?.roleCode === "dorm_admin") {
        system.setPrimaryColor("#4566d5")
    } else {
        system.setPrimaryColor("#00b781")
    }
    // #endif
}

// 应用显示时的处理函数
const handleShow = () => {
    // #ifdef MP-WEIXIN
    console.log("App Show", "微信小程序")
    // #endif
    // #ifdef APP-PLUS
    console.log("App Show", "APP")
    // #endif
    // #ifdef H5-WEIXIN
    console.log("App Show", "微信浏览器H5")
    // #endif
    // #ifdef H5
    console.log("App Show", "普通H5")
    // #endif
    console.log("App Show", process.env.NODE_ENV, import.meta.env.MODE, import.meta.env.VITE_BASE_API)
}
// #ifdef H5-WEIXIN || H5
// VConsole调试工具
let tapCount = 0
let lastTapTime = 0

// 处理点击事件用于调试
const handleTap = () => {
    const now = new Date().getTime()
    if (now - lastTapTime < 500) {
        // 如果两次点击间隔小于500毫秒
        tapCount++
    } else {
        tapCount = 1 // 重置计数器
    }
    lastTapTime = now
    console.log(tapCount, "tapCount")
    if (tapCount === 15) {
        import("vconsole").then((VConsole) => {
            new VConsole.default()
        })
        tapCount = 0 // 重置计数器
    }
}

// 添加点击事件监听器
// document.addEventListener("click", handleTap)
// 如果需要在某个时刻移除监听器，可以使用下面的代码
// document.removeEventListener("click", handleTap)
// #endif

onLaunch(async (options) => {
    const { user } = useStore()
    handleLaunch(options)
    // #ifdef H5
    if (user.sigGen?.sdkAppId) {
        // 检查TIM是否已经就绪，避免重复登录
        if (isTIMReady()) {
            console.log("TIM已经就绪，跳过重复登录")
            return
        }

        const { sdkAppId, identifier, userSig } = user.sigGen
        TUILogin.login({
            SDKAppID: sdkAppId,
            userID: identifier,
            userSig: userSig,
            useUploadPlugin: true,
            framework: `vue3`
        })
            .then((res) => {
                console.log("TUILogin登录成功", res)
            })
            .catch(() => {})
    }
    // #endif
})

onShow(() => {
    handleShow()
})
</script>

<style lang="scss">
@import "./styles/iconfont.css";
:root {
    /* 适配小程序顶部状态栏的高度 */
    --primary-color: #00b781;
    --primary-bg-color: #00b78110;
}
/* #ifdef MP-WEIXIN */
page,
apps {
    /* 适配小程序顶部状态栏的高度 */
    --primary-color: #00b781;
    --primary-bg-color: #00b78110;
}
/* #endif */

uni-page-body,
html,
body,
page {
    width: 100% !important;
    height: 100% !important;
    overflow: hidden;
}
</style>
