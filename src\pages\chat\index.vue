<template>
    <yd-page-view class="chat" :title="pageTitle" :hideBottom="false" :hideLeft="true" :leftWidth="navBarLeftWidth(80)" :rightWidth="navBarRightWidth(80)" :tabBarCurrent="isDormAdmin ? 3 : 2">
        <!-- #ifdef H5 -->
        <template #right>
            <div class="address" @click="gotoAddressBook">
                通讯录
                <image class="address_logo" mode="widthFix" :src="addressLogo" alt="" />
            </div>
        </template>
        <!-- #endif -->
        <!-- #ifdef APP-PLUS -->
        <template #right>
            <div class="address" @click="gotoAddressBook">
                通讯录
                <image class="address_logo" mode="widthFix" :src="addressLogo" alt="" />
            </div>
        </template>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
        <div class="address" @click="gotoAddressBook">
            通讯录
            <image class="address_logo" mode="widthFix" :src="addressLogo" alt="" />
        </div>
        <!-- #endif -->
        <div class="list">
            <div class="item" v-for="item in fixedList" :key="item.type" @click="gotoDetail(item)">
                <div class="left_avatar">
                    <uni-badge
                        :customStyle="{ background: '#FB2D24' }"
                        :text="(item.type == 'todo' || item.type == 'tim') && item.total > 0 ? item.total : null"
                        absolute="rightTop"
                        size="small"
                    >
                        <image class="avatar" mode="widthFix" :src="item.url" alt="" />
                    </uni-badge>
                </div>
                <div class="content">
                    <div class="title">
                        <div class="name">{{ item.title }}</div>
                        <div class="time" v-if="item.time">{{ item.time }}</div>
                    </div>
                    <div class="content_text ellipsis" v-if="item.type != 'addressBook'">{{item.type == 'tim' ? `${item.total}条未读消息` : item.content || `暂无${item.title}` }}</div>
                </div>
            </div>
        </div>
    </yd-page-view>
</template>

<script setup>
import useStore from "@/store"
import { onLoad, onShow } from "@dcloudio/uni-app"
const { user } = useStore()
// #ifdef H5
import { TUILogin } from "@tencentcloud/tui-core"
// #endif
import { getTIMUnreadCount, watchTIMUnreadCount, isTIMReady } from "@/utils/timUnreadHelper.js"
const pageTitle = ref("消息")
const addressLogo = computed(() => {
    return user.identityInfo?.roleCode === "dorm_admin" ? "@nginx/chat/dormAddressLogo.png" : "@nginx/chat/address_logo2.png"
})

// 腾讯IM未读消息总数
const timUnreadCount = ref(0)

const fixedList = ref([
    {
        type: "todo",
        title: "待办",
        url: "@nginx/chat/todo_logo.png",
        content: "",
        time: "",
        total: 0
    },
    {
        type: "message",
        title: "消息通知",
        url: "@nginx/chat/message_logo.png",
        content: "",
        time: ""
    },
    {
        type: "tim",
        title: "留言",
        url: "@nginx/chat/tim.png",
        total: 0
    }
])

// 是否为宿管身份
const isDormAdmin = computed(() => {
    return user.identityInfo.roleCode === "dorm_admin"
})

function gotoDetail(item) {
    navigateTo({
        url: `/package/chat/${item.type}/index`
    })
}

// 获取待办
function getTodo() {
    http.post("/app/ruTask/page", {
        pageNo: 1,
        pageSize: 1,
        status: 1 // 0全部 1待办 2已办
    }).then((res) => {
        const title = res.data?.list[0]?.title || res.data?.list[0]?.subtitle
        const startTime = res.data?.list[0]?.startTime
        fixedList.value = fixedList.value.map((i) => {
            return {
                ...i,
                total: i.type == "todo" ? res.data.total : i.total,
                time: i.type == "todo" ? startTime : i.time,
                content: i.type == "todo" ? title : i.content
            }
        })
    })
}

// 获取消息通知
function getMessage() {
    http.post("/app/v2/push/pageUserMessage", {
        pageNo: 1,
        pageSize: 1
    }).then((res) => {
        const title = res.data?.list[0]?.title || "-"
        const sendTime = res.data?.list[0]?.sendTime
        fixedList.value = fixedList.value.map((i) => {
            return {
                ...i,
                time: i.type == "message" ? sendTime : i.time,
                content: i.type == "message" ? title : i.content
            }
        })
    })
}

function gotoAddressBook() {
    navigateTo({
        url: "/package/chat/addressBook/index"
    })
}

// 获取腾讯IM未读消息总数并更新界面
function refreshTIMUnreadCount() {
    getTIMUnreadCount()
        .then((count) => {
            timUnreadCount.value = count
            updateTIMTotal(count)
        })
        .catch((error) => {
            console.error('获取IM未读消息数失败:', error)
            // 失败时设置为0
            timUnreadCount.value = 0
            updateTIMTotal(0)
        })
}

// 更新留言项的未读消息数
function updateTIMTotal(count) {
    fixedList.value = fixedList.value.map((item) => {
        if (item.type === 'tim') {
            return {
                ...item,
                total: count
            }
        }
        return item
    })
}

// 监听TUIStore中的未读消息数变化
function startWatchingTIMUnreadCount() {
    watchTIMUnreadCount((count) => {
        timUnreadCount.value = count
        updateTIMTotal(count)
    })
}

onMounted(() => {
    getMessage()
    getTodo()
    // #ifdef H5
    // 监听IM未读消息数变化
    startWatchingTIMUnreadCount()
    // 延迟获取IM未读消息数，确保TUILogin已完成
    setTimeout(() => {
        refreshTIMUnreadCount()
    }, 1000)
    // #endif
})

onLoad(() => {
    // #ifdef H5
    // 检查TIM是否已经就绪，避免重复登录
    if (isTIMReady()) {
        console.log("TIM已经就绪，直接获取未读消息数")
        // 如果TIM已经就绪，直接获取未读消息数
        setTimeout(() => {
            refreshTIMUnreadCount()
        }, 300)
        return
    }

    const { sdkAppId, identifier, userSig } = user.sigGen
    TUILogin.login({
        SDKAppID: sdkAppId,
        userID: identifier,
        userSig: userSig,
        useUploadPlugin: true,
        framework: `vue3`
    })
        .then((res) => {
            console.log("TUILogin登录成功2", res)
            // 登录成功后获取未读消息数
            setTimeout(() => {
                refreshTIMUnreadCount()
            }, 500)
        })
        .catch(() => {})
    // #endif
})

onShow(() => {
    // #ifdef H5
    // 页面显示时刷新未读消息数
    setTimeout(() => {
        refreshTIMUnreadCount()
    }, 300)
    // #endif
})
</script>

<style lang="scss" scoped>
.chat {
    .address {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-weight: 400;
        font-size: 28rpx;
        color: var(--primary-color);
        line-height: 40rpx;
        .address_logo {
            margin-left: 10rpx;
            width: 44rpx;
            height: 44rpx;
        }
    }
    // #ifdef MP-WEIXIN
    .address {
        padding: 10rpx 30rpx;
        justify-content: flex-start;
    }
    // #endif
    .list {
        width: 100%;
        background: $uni-bg-color;
        .item {
            width: calc(100vw - 60rpx);
            display: flex;
            align-items: center;
            padding: 30rpx;
            .left_avatar {
                width: 100rpx;
                height: 100rpx;
                .avatar {
                    width: 100rpx;
                    height: 100rpx;
                }
            }
            .content {
                padding: 4rpx 0rpx;
                flex: 1;
                height: 100rpx;
                margin-left: 20rpx;
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                .title {
                    display: flex;
                    justify-content: space-between;
                    .name {
                        color: var(--primary-color);
                        font-weight: 500;
                        font-size: 30rpx;
                        line-height: 42rpx;
                        text-align: left;
                    }
                    .time {
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #999999;
                        line-height: 34rpx;
                        text-align: right;
                    }
                }
                .content_text {
                    width: 100%;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #666666;
                    line-height: 40rpx;
                    text-align: left;
                    padding: 0rpx;
                }
            }
        }
    }
}
</style>
