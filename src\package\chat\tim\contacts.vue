<!-- 通讯录 -->
<template>
    <CustomTabbar :tab-list="productTabList">
        <TUIContact />
    </CustomTabbar>
</template>

<script setup>
import CustomTabbar from "./customTabbar.vue"
import TUIContact from "@/TUIKit/components/TUIContact/index.vue"
import useStore from "@/store"
import { onLoad, onShow } from "@dcloudio/uni-app"
const { user } = useStore()

console.log(user, "123123123")

// tabbar
const productTabList = [
    {
        text: "消息",
        pagePath: "/package/chat/tim/index",
        iconPath: "@nginx/home/<USER>/newsUnSelect.png",
        selectedIconPath: "/@nginx/workbench/dormManage/newsSelect.png"
    },
    {
        text: "通讯录",
        pagePath: "/package/chat/tim/contacts",
        iconPath: "@nginx/home/<USER>/homeUnSelect.png",
        selectedIconPath: "@nginx/home/<USER>/homeSelect.png"
    }
]

onBackPress(() => {
    uni.switchTab({
        url: "/pages/chat/index"
    })
    return true
})
</script>
