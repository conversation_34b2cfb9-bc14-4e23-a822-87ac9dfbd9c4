<!-- 通讯录 -->
<template>
    <!-- #ifdef H5 -->
    <CustomTabbar :tab-list="productTabList">
        <TUIContact />
    </CustomTabbar>
    <!-- #endif -->
    <!-- #ifdef MP-WEIXIN -->
    <div class="tim-placeholder">
        <div class="placeholder-content">
            <div class="placeholder-icon">📞</div>
            <div class="placeholder-text">此功能仅在H5版本中可用</div>
            <div class="placeholder-desc">请使用浏览器访问完整功能</div>
        </div>
    </div>
    <!-- #endif -->
</template>

<script setup>
import CustomTabbar from "./customTabbar.vue"
// #ifdef H5
import TUIContact from "@/TUIKit/components/TUIContact/index.vue"
// #endif
import useStore from "@/store"
import { onLoad, onShow } from "@dcloudio/uni-app"
const { user } = useStore()

console.log(user, "123123123")

// tabbar
const productTabList = [
    {
        text: "消息",
        pagePath: "/package/chat/tim/index",
        iconPath: "@nginx/home/<USER>/newsUnSelect.png",
        selectedIconPath: "/@nginx/workbench/dormManage/newsSelect.png"
    },
    {
        text: "通讯录",
        pagePath: "/package/chat/tim/contacts",
        iconPath: "@nginx/home/<USER>/homeUnSelect.png",
        selectedIconPath: "@nginx/home/<USER>/homeSelect.png"
    }
]

onBackPress(() => {
    uni.switchTab({
        url: "/pages/chat/index"
    })
    return true
})
</script>

<style lang="scss" scoped>
// #ifdef MP-WEIXIN
.tim-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: #f5f5f5;

    .placeholder-content {
        text-align: center;
        padding: 60rpx;

        .placeholder-icon {
            font-size: 120rpx;
            margin-bottom: 40rpx;
        }

        .placeholder-text {
            font-size: 32rpx;
            color: #333;
            margin-bottom: 20rpx;
            font-weight: 500;
        }

        .placeholder-desc {
            font-size: 28rpx;
            color: #666;
            line-height: 1.5;
        }
    }
}
// #endif
</style>
