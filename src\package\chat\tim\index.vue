<!-- 消息 -->
<template>
    <CustomTabbar :tab-list="productTabList">
        <TUIConversation />
    </CustomTabbar>
</template>

<script setup>
import CustomTabbar from "./customTabbar.vue"
import TUIConversation from "@/TUIKit/components/TUIConversation/index.vue"
import useStore from "@/store"
import { onLoad, onShow } from "@dcloudio/uni-app"

const { user } = useStore()
console.log(user, "123123123")
// tabbar
const productTabList = [
    {
        text: "消息",
        pagePath: "/package/chat/tim/index",
        iconPath: "@nginx/home/<USER>/newsUnSelect.png",
        selectedIconPath: "@nginx/home/<USER>/newsSelect.png"
    },
    {
        text: "通讯录",
        pagePath: "/package/chat/tim/contacts",
        iconPath: "@nginx/home/<USER>/homeUnSelect.png",
        selectedIconPath: "@nginx/home/<USER>/homeSelect.png"
    }
]


onBackPress(() => {
    uni.switchTab({
        url: "/pages/chat/index"
    })
    return true
})
</script>
